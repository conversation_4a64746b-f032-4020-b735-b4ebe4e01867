import 'package:device_apps/device_apps.dart';
import 'package:flutter/foundation.dart';

class AppDetectionService {
  static const Map<String, List<String>> _appPackageMap = {
    'WhatsApp': [
      'com.whatsapp',
      'com.whatsapp.w4b', // WhatsApp Business
    ],
    'Messenger': [
      'com.facebook.orca',
      'com.facebook.mlite', // Messenger Lite
    ],
    'Telegram': [
      'org.telegram.messenger',
      'org.telegram.plus',
      'org.thunderdog.challegram',
    ],
    'Instagram': [
      'com.instagram.android',
      'com.instagram.lite',
    ],
    'Facebook': [
      'com.facebook.katana',
      'com.facebook.lite',
    ],
    'iMessage': [
      // iMessage is iOS only, but we can check for similar messaging apps
      'com.apple.MobileSMS', // This won't exist on Android
    ],
    'Botim': [
      'im.thebot.messenger',
    ],
    'IMO': [
      'com.imo.android.imoim',
    ],
    'Snapchat': [
      'com.snapchat.android',
    ],
    'Viber': [
      'com.viber.voip',
    ],
    'Google Chat': [
      'com.google.android.apps.dynamite',
      'com.google.android.talk',
    ],
    'LINE': [
      'jp.naver.line.android',
    ],
  };

  static Future<bool> isAppInstalled(String appName) async {
    try {
      final packageNames = _appPackageMap[appName];
      if (packageNames == null || packageNames.isEmpty) {
        return false;
      }

      // Check each possible package name for the app
      for (final packageName in packageNames) {
        final isInstalled = await DeviceApps.isAppInstalled(packageName);
        if (isInstalled) {
          return true;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<List<String>> getInstalledSocialApps() async {
    final List<String> installedApps = [];

    try {
      // Check each app in our mapping
      for (final appName in _appPackageMap.keys) {
        final isInstalled = await isAppInstalled(appName);
        if (isInstalled) {
          installedApps.add(appName);
        }
      }

      return installedApps;
    } catch (e) {
      return [];
    }
  }


  static bool get isAppDetectionSupported {
    return defaultTargetPlatform == TargetPlatform.android;
  }

  
  
  
}
