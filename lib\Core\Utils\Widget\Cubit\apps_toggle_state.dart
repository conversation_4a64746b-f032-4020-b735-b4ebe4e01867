part of 'apps_toggle_cubit.dart';

@immutable
sealed class AppsToggleState {}

final class AppsToggleInitial extends AppsToggleState {}

final class AppsToggleLoading extends AppsToggleState {}

final class AppsToggleLoaded extends AppsToggleState {
  final List<SocialAppModel> allApps;
  final List<SocialAppModel> installedApps;
  final bool isAppDetectionSupported;

  final Map<String, bool> appInstallationStatus;
  AppsToggleLoaded({
    required this.allApps,
    required this.installedApps,
    required this.isAppDetectionSupported,
    required this.appInstallationStatus,
  });

  AppsToggleLoaded copyWith({
    List<SocialAppModel>? allApps,
    List<SocialAppModel>? installedApps,
    bool? isAppDetectionSupported,
    Map<String, bool>? appInstallationStatus,
  }) {
    return AppsToggleLoaded(
      allApps: allApps ?? this.allApps,
      installedApps: installedApps ?? this.installedApps,
      isAppDetectionSupported:
          isAppDetectionSupported ?? this.isAppDetectionSupported,
      appInstallationStatus:
          appInstallationStatus ?? this.appInstallationStatus,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppsToggleLoaded &&
        listEquals(other.allApps, allApps) &&
        listEquals(other.installedApps, installedApps) &&
        other.isAppDetectionSupported == isAppDetectionSupported &&
        mapEquals(other.appInstallationStatus, appInstallationStatus);
  }

  @override
  int get hashCode {
    return Object.hash(
      allApps,
      installedApps,
      isAppDetectionSupported,
      appInstallationStatus,
    );
  }
}

final class AppsToggleError extends AppsToggleState {
  final String message;

  final AppsToggleState? previousState;

  AppsToggleError({required this.message, this.previousState});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppsToggleError &&
        other.message == message &&
        other.previousState == previousState;
  }

  @override
  int get hashCode => Object.hash(message, previousState);
}

final class AppsToggleUpdating extends AppsToggleState {
  final AppsToggleLoaded loadedState;
  final String updatingAppId;

  AppsToggleUpdating({required this.loadedState, required this.updatingAppId});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppsToggleUpdating &&
        other.loadedState == loadedState &&
        other.updatingAppId == updatingAppId;
  }

  @override
  int get hashCode => Object.hash(loadedState, updatingAppId);
}
