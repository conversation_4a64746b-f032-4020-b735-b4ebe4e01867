import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class OverlayService {
  static const MethodChannel _channel = MethodChannel(
    'com.example.al_tarjuman/overlay',
  );

  /// Check if the app has overlay permission
  static Future<bool> checkOverlayPermission() async {
    try {
      final bool hasPermission = await _channel.invokeMethod(
        'checkOverlayPermission',
      );
      return hasPermission;
    } on PlatformException catch (e) {
      debugPrint("Failed to check overlay permission: ${e.message}");
      return false;
    }
  }

  /// Request overlay permission
  static Future<void> requestOverlayPermission() async {
    try {
      await _channel.invokeMethod('requestOverlayPermission');
    } on PlatformException catch (e) {
      debugPrint("Failed to request overlay permission: ${e.message}");
    }
  }

  /// Start the overlay service
  static Future<bool> startOverlayService() async {
    try {
      final bool success = await _channel.invokeMethod('startOverlayService');
      return success;
    } on PlatformException catch (e) {
      debugPrint("Failed to start overlay service: ${e.message}");
      return false;
    }
  }

  /// Stop the overlay service
  static Future<bool> stopOverlayService() async {
    try {
      final bool success = await _channel.invokeMethod('stopOverlayService');
      return success;
    } on PlatformException catch (e) {
      debugPrint("Failed to stop overlay service: ${e.message}");
      return false;
    }
  }
}
