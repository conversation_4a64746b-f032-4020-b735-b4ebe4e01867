import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'Config/Cubit/settings_cubit.dart';
import 'Config/Routes/Navigation/navigation_service.dart';
import 'Config/build_root_starting.dart';
import 'Core/Storage/Local/local_storage_service.dart';
import 'Core/Storage/Models/social_app_model.dart';
import 'Core/Storage/Local/social_apps_storage_service.dart';

final AppNavigationService kNavigationService = AppNavigationService();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();

  Hive.registerAdapter(SocialAppModelAdapter());

  await LocalStorageService.init();
  await SocialAppsStorageService.init();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => SettingsCubit(), lazy: true),
      ],
      child: BlocBuilder<SettingsCubit, SettingsState>(
        builder: (context, state) => ScreenUtilInit(
          designSize: const Size(375, 812),
          minTextAdapt: true,
          splitScreenMode: true,
          useInheritedMediaQuery: true,
          enableScaleWH: () => false,
          enableScaleText: () => true,
          builder: (context, child) => BuildRootStarting(
            state: state,
            navigatorKey: kNavigationService.navigatorKey,
          ),
        ),
      ),
    );
  }
}
