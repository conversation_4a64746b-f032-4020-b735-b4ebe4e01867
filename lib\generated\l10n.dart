// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `The translator`
  String get name_app {
    return Intl.message(
      'The translator',
      name: 'name_app',
      desc: '',
      args: [],
    );
  }

  /// `Translate instantly with The translator, thanks to the power of artificial intelligence.`
  String get title_card {
    return Intl.message(
      'Translate instantly with The translator, thanks to the power of artificial intelligence.',
      name: 'title_card',
      desc: '',
      args: [],
    );
  }

  /// `Use The translator to translate texts now.`
  String get translate_now {
    return Intl.message(
      'Use The translator to translate texts now.',
      name: 'translate_now',
      desc: '',
      args: [],
    );
  }

  /// `Chat with The translator now.`
  String get chat_now {
    return Intl.message(
      'Chat with The translator now.',
      name: 'chat_now',
      desc: '',
      args: [],
    );
  }

  /// `Use The translator to translate audio now.`
  String get micro_now {
    return Intl.message(
      'Use The translator to translate audio now.',
      name: 'micro_now',
      desc: '',
      args: [],
    );
  }

  /// `Search language...`
  String get search_language {
    return Intl.message(
      'Search language...',
      name: 'search_language',
      desc: '',
      args: [],
    );
  }

  /// `Write the translation here...`
  String get live_translation {
    return Intl.message(
      'Write the translation here...',
      name: 'live_translation',
      desc: '',
      args: [],
    );
  }

  /// `Enjoy a smooth and instant translation experience with The translator, where you can easily and quickly translate texts, words, and audio recordings into your preferred language. Thanks to the advanced artificial intelligence technologies that ensure high accuracy and a pleasant user experience.`
  String get description_setting_app {
    return Intl.message(
      'Enjoy a smooth and instant translation experience with The translator, where you can easily and quickly translate texts, words, and audio recordings into your preferred language. Thanks to the advanced artificial intelligence technologies that ensure high accuracy and a pleasant user experience.',
      name: 'description_setting_app',
      desc: '',
      args: [],
    );
  }

  /// `Allow The translator to translate your audio recordings automatically and send them in the selected language using artificial intelligence.`
  String get allow_the_app_to_translate_your_recordings {
    return Intl.message(
      'Allow The translator to translate your audio recordings automatically and send them in the selected language using artificial intelligence.',
      name: 'allow_the_app_to_translate_your_recordings',
      desc: '',
      args: [],
    );
  }

  /// `Allow The translator to translate texts automatically and send them in the selected language using artificial intelligence.`
  String get allow_the_app_to_translate_texts {
    return Intl.message(
      'Allow The translator to translate texts automatically and send them in the selected language using artificial intelligence.',
      name: 'allow_the_app_to_translate_texts',
      desc: '',
      args: [],
    );
  }

  /// `The symbol appears on the screen`
  String get symbol_appears_on_the_screen {
    return Intl.message(
      'The symbol appears on the screen',
      name: 'symbol_appears_on_the_screen',
      desc: '',
      args: [],
    );
  }

  /// `Allow The translator to import images from the gallery`
  String get enable_pick_image {
    return Intl.message(
      'Allow The translator to import images from the gallery',
      name: 'enable_pick_image',
      desc: '',
      args: [],
    );
  }

  /// `Allow The translator to import files from the gallery`
  String get enable_pick_file {
    return Intl.message(
      'Allow The translator to import files from the gallery',
      name: 'enable_pick_file',
      desc: '',
      args: [],
    );
  }

  /// `Allow The translator to use the camera`
  String get enable_camera {
    return Intl.message(
      'Allow The translator to use the camera',
      name: 'enable_camera',
      desc: '',
      args: [],
    );
  }

  /// `Allow The translator to use the microphone`
  String get enable_microphone {
    return Intl.message(
      'Allow The translator to use the microphone',
      name: 'enable_microphone',
      desc: '',
      args: [],
    );
  }

  /// `Language`
  String get language {
    return Intl.message(
      'Language',
      name: 'language',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
