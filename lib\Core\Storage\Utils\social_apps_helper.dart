import '../Models/social_app_model.dart';
import '../Services/social_apps_storage_service.dart';

/// Helper class providing convenient methods for social apps storage operations
/// This class acts as a simplified interface to the SocialAppsStorageService
class SocialAppsHelper {
  /// Get all social apps with their current states
  static List<SocialAppModel> getAllApps() {
    return SocialAppsStorageService.getAllSocialApps();
  }

  /// Get only enabled social apps
  static List<SocialAppModel> getEnabledApps() {
    return SocialAppsStorageService.getEnabledApps();
  }

  /// Get only disabled social apps
  static List<SocialAppModel> getDisabledApps() {
    return SocialAppsStorageService.getDisabledApps();
  }

  /// Toggle a specific app's state
  static Future<bool> toggleApp(String appId) async {
    final app = SocialAppsStorageService.getSocialAppById(appId);
    if (app != null) {
      return await SocialAppsStorageService.updateAppToggleState(
        appId,
        !app.isEnabled,
      );
    }
    return false;
  }

  /// Enable a specific app
  static Future<bool> enableApp(String appId) async {
    return await SocialAppsStorageService.updateAppToggleState(appId, true);
  }

  /// Disable a specific app
  static Future<bool> disableApp(String appId) async {
    return await SocialAppsStorageService.updateAppToggleState(appId, false);
  }

  /// Enable all apps
  static Future<bool> enableAllApps() async {
    return await SocialAppsStorageService.enableAllApps();
  }

  /// Disable all apps
  static Future<bool> disableAllApps() async {
    return await SocialAppsStorageService.disableAllApps();
  }

  /// Reset all apps to default state (disabled)
  static Future<bool> resetToDefaults() async {
    return await SocialAppsStorageService.resetToDefaults();
  }

  /// Get count of enabled apps
  static int getEnabledCount() {
    return SocialAppsStorageService.getEnabledAppsCount();
  }

  /// Check if a specific app is enabled
  static bool isAppEnabled(String appId) {
    return SocialAppsStorageService.isAppEnabled(appId);
  }

  /// Get app by name (case-insensitive)
  static SocialAppModel? getAppByName(String name) {
    return SocialAppsStorageService.getSocialAppByName(name);
  }

  /// Get app by ID
  static SocialAppModel? getAppById(String id) {
    return SocialAppsStorageService.getSocialAppById(id);
  }

  /// Get social apps in the legacy Map format for backward compatibility
  static List<Map<String, dynamic>> getAppsAsMapList() {
    return SocialAppsStorageService.getSocialAppsAsMapList();
  }

  /// Get enabled apps as a simple list of names
  static List<String> getEnabledAppNames() {
    return getEnabledApps().map((app) => app.name).toList();
  }

  /// Get disabled apps as a simple list of names
  static List<String> getDisabledAppNames() {
    return getDisabledApps().map((app) => app.name).toList();
  }

  /// Enable multiple apps by their IDs
  static Future<bool> enableMultipleApps(List<String> appIds) async {
    bool allSuccessful = true;
    for (final appId in appIds) {
      final success = await enableApp(appId);
      if (!success) allSuccessful = false;
    }
    return allSuccessful;
  }

  /// Disable multiple apps by their IDs
  static Future<bool> disableMultipleApps(List<String> appIds) async {
    bool allSuccessful = true;
    for (final appId in appIds) {
      final success = await disableApp(appId);
      if (!success) allSuccessful = false;
    }
    return allSuccessful;
  }

  /// Enable multiple apps by their names
  static Future<bool> enableMultipleAppsByName(List<String> appNames) async {
    bool allSuccessful = true;
    for (final appName in appNames) {
      final app = getAppByName(appName);
      if (app != null) {
        final success = await enableApp(app.id);
        if (!success) allSuccessful = false;
      } else {
        allSuccessful = false;
      }
    }
    return allSuccessful;
  }

  /// Disable multiple apps by their names
  static Future<bool> disableMultipleAppsByName(List<String> appNames) async {
    bool allSuccessful = true;
    for (final appName in appNames) {
      final app = getAppByName(appName);
      if (app != null) {
        final success = await disableApp(app.id);
        if (!success) allSuccessful = false;
      } else {
        allSuccessful = false;
      }
    }
    return allSuccessful;
  }

  /// Get a summary of current app states
  static Map<String, dynamic> getAppsSummary() {
    final allApps = getAllApps();
    final enabledApps = getEnabledApps();
    final disabledApps = getDisabledApps();

    return {
      'totalApps': allApps.length,
      'enabledCount': enabledApps.length,
      'disabledCount': disabledApps.length,
      'enabledApps': enabledApps.map((app) => app.name).toList(),
      'disabledApps': disabledApps.map((app) => app.name).toList(),
      'lastUpdated': allApps.isNotEmpty
          ? allApps
              .map((app) => app.lastUpdated)
              .reduce((a, b) => a.isAfter(b) ? a : b)
          : null,
    };
  }
}
