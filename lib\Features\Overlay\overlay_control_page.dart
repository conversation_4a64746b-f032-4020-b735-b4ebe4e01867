import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../Core/Resources/text_style.dart';
import '../../Core/Services/overlay_service.dart';
import '../../Core/Utils/Widget/default_app_bar.dart';

class OverlayControlPage extends StatefulWidget {
  const OverlayControlPage({Key? key}) : super(key: key);

  @override
  State<OverlayControlPage> createState() => _OverlayControlPageState();
}

class _OverlayControlPageState extends State<OverlayControlPage> {
  bool _hasPermission = false;
  bool _isServiceRunning = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkPermission();
  }

  Future<void> _checkPermission() async {
    setState(() {
      _isLoading = false;
    });
    final hasPermission = await OverlayService.checkOverlayPermission();
    if (hasPermission) {
      setState(() {
        _hasPermission = hasPermission;
        _isLoading = false;
      });
    } else {
      _requestPermission();
    }
  }

  Future<void> _requestPermission() async {
    await OverlayService.requestOverlayPermission();
    await _checkPermission();
  }

  Future<void> _toggleService() async {
    setState(() {
      _isLoading = true;
    });

    bool success;
    if (_isServiceRunning) {
      success = await OverlayService.stopOverlayService();
    } else {
      success = await OverlayService.startOverlayService();
    }

    if (success) {
      setState(() {
        _isServiceRunning = !_isServiceRunning;
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('An error occurred')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(arowback: true),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(16.0.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chat Translation Overlay',
                      style: AppTextStyles.h5Bold,
                    ),
                    SizedBox(height: 16.h),
                    Container(
                      padding: EdgeInsets.all(16.0.w),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.blue, width: 2.0),
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Text(
                        'This feature allows Al Tarjuman to display a floating bubble over other apps for quick translations.',
                        style: AppTextStyles.bodyMediumMedium,
                      ),
                    ),
                    SizedBox(height: 24.h),
                    if (!_hasPermission)
                      ElevatedButton(
                        onPressed: _requestPermission,
                        child: Text('Grant Overlay Permission'),
                      )
                    else
                      SwitchListTile(
                        title: Text('Enable Translation Overlay'),
                        subtitle: Text(
                          _isServiceRunning
                              ? 'Translation bubble is active'
                              : 'Translation bubble is inactive',
                        ),
                        value: _isServiceRunning,
                        onChanged: (value) => _toggleService(),
                      ),
                    SizedBox(height: 16.h),
                    if (_isServiceRunning)
                      Container(
                        padding: EdgeInsets.all(16.0.w),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'How to use:',
                              style: AppTextStyles.bodyLargeSemiBold,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              '1. The blue bubble will appear on your screen\n'
                              '2. Drag it anywhere on the screen\n'
                              '3. Tap it to open the translation panel\n'
                              '4. Copy text from any chat app\n'
                              '5. The translation will appear automatically',
                              style: AppTextStyles.bodyMediumMedium,
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
    );
  }
}
