package com.example.al_tarjuman.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.example.al_tarjuman.MainActivity

class OverlayService : Service() {
    private lateinit var windowManager: WindowManager
    private lateinit var overlayView: View
    private lateinit var translationBubble: View
    private lateinit var clipboardManager: ClipboardManager
    private var isExpanded = false
    private var lastClipboardText = ""
    private val handler = Handler(Looper.getMainLooper())
    private var isDragging = false

    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        createOverlayView()
        startClipboardMonitoring()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        createNotificationChannel()
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
        
        return START_STICKY
    }

    private fun createOverlayView() {
        // Create the floating bubble
        translationBubble = LayoutInflater.from(this).inflate(
            resources.getIdentifier("translation_bubble", "layout", packageName), null
        )

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_SYSTEM_ALERT,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        )

        params.gravity = Gravity.TOP or Gravity.START
        params.x = 0
        params.y = 100

        // Set up drag functionality
        setupDragFunctionality(translationBubble, params)

        // Set up click to expand
        val bubbleIcon = translationBubble.findViewById<ImageView>(
            resources.getIdentifier("bubbleIcon", "id", packageName)
        )
        bubbleIcon?.setOnClickListener {
            if (!isDragging) {
                toggleTranslationPanel()
            }
        }

        windowManager.addView(translationBubble, params)
    }
    
    private fun setupDragFunctionality(view: View, params: WindowManager.LayoutParams) {
        view.setOnTouchListener(object : View.OnTouchListener {
            private var initialX: Int = 0
            private var initialY: Int = 0
            private var initialTouchX: Float = 0f
            private var initialTouchY: Float = 0f
            private var startTime: Long = 0

            override fun onTouch(v: View?, event: MotionEvent): Boolean {
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        initialX = params.x
                        initialY = params.y
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        startTime = System.currentTimeMillis()
                        isDragging = false
                        return true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        val deltaX = event.rawX - initialTouchX
                        val deltaY = event.rawY - initialTouchY

                        // Check if movement is significant enough to be considered dragging
                        if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
                            isDragging = true
                            params.x = initialX + deltaX.toInt()
                            params.y = initialY + deltaY.toInt()
                            windowManager.updateViewLayout(view, params)
                        }
                        return true
                    }
                    MotionEvent.ACTION_UP -> {
                        val endTime = System.currentTimeMillis()
                        // Reset dragging flag after a short delay
                        handler.postDelayed({
                            isDragging = false
                        }, 100)
                        return true
                    }
                }
                return false
            }
        })
    }

    private fun startClipboardMonitoring() {
        val clipboardRunnable = object : Runnable {
            override fun run() {
                try {
                    val clipData = clipboardManager.primaryClip
                    if (clipData != null && clipData.itemCount > 0) {
                        val clipText = clipData.getItemAt(0).text?.toString() ?: ""
                        if (clipText.isNotEmpty() && clipText != lastClipboardText) {
                            lastClipboardText = clipText
                            Log.d("OverlayService", "New clipboard text: $clipText")
                            // Show translation panel with new text
                            showTranslationWithText(clipText)
                        }
                    }
                } catch (e: Exception) {
                    Log.e("OverlayService", "Error monitoring clipboard: ${e.message}")
                }
                // Check again after 1 second
                handler.postDelayed(this, 1000)
            }
        }
        handler.post(clipboardRunnable)
    }

    private fun showTranslationWithText(text: String) {
        if (!isExpanded) {
            createExpandedOverlay()
            isExpanded = true
        }

        // Update the text in the overlay
        try {
            val originalTextView = overlayView.findViewById<TextView>(
                resources.getIdentifier("originalText", "id", packageName)
            )
            val translatedTextView = overlayView.findViewById<TextView>(
                resources.getIdentifier("translatedText", "id", packageName)
            )

            originalTextView?.text = text
            translatedTextView?.text = "Translating..." // You can implement actual translation here

            // Simulate translation (replace with actual translation API call)
            handler.postDelayed({
                translatedTextView?.text = "Translation: $text" // Replace with actual translation
            }, 1000)

        } catch (e: Exception) {
            Log.e("OverlayService", "Error updating translation text: ${e.message}")
        }
    }

    private fun toggleTranslationPanel() {
        if (isExpanded) {
            // Collapse the panel
            windowManager.removeView(overlayView)
        } else {
            // Expand the panel
            createExpandedOverlay()
        }
        isExpanded = !isExpanded
    }
    
    private fun createExpandedOverlay() {
        overlayView = LayoutInflater.from(this).inflate(
            resources.getIdentifier("translation_overlay", "layout", packageName), null
        )

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_SYSTEM_ALERT,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        )

        params.gravity = Gravity.BOTTOM

        // Set up close button
        val closeButton = overlayView.findViewById<ImageView>(
            resources.getIdentifier("closeButton", "id", packageName)
        )
        closeButton?.setOnClickListener {
            toggleTranslationPanel()
        }

        windowManager.addView(overlayView, params)
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Translation Service",
                NotificationManager.IMPORTANCE_LOW
            )
            val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) 
                PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            else 
                PendingIntent.FLAG_UPDATE_CURRENT
        )
        
        return Notification.Builder(this, CHANNEL_ID)
            .setContentTitle("Al Tarjuman")
            .setContentText("Translation service is running")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setContentIntent(pendingIntent)
            .build()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        if (::translationBubble.isInitialized) {
            windowManager.removeView(translationBubble)
        }
        if (::overlayView.isInitialized && isExpanded) {
            windowManager.removeView(overlayView)
        }
    }

    companion object {
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "TranslationServiceChannel"
    }
}