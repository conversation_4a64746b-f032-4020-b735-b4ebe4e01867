import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../Storage/Models/social_app_model.dart';
import 'Cubit/apps_toggle_cubit.dart';

class AppsToggle extends StatelessWidget {
  const AppsToggle({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AppsToggleCubit()..loadApps(),
      child: const _AppsToggleView(),
    );
  }
}

class _AppsToggleView extends StatelessWidget {
  const _AppsToggleView();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppsToggleCubit, AppsToggleState>(
      builder: (context, state) {
        return switch (state) {
          AppsToggleInitial() => const _LoadingWidget(),
          AppsToggleLoading() => const _LoadingWidget(),
          AppsToggleUpdating() => _LoadedWidget(
            apps: state.loadedState.installedApps,
            updatingAppId: state.updatingAppId,
          ),
          AppsToggleLoaded() => _LoadedWidget(apps: state.installedApps),
          AppsToggleError() => _ErrorWidget(
            message: state.message,
            onRetry: () => context.read<AppsToggleCubit>().recoverFromError(),
          ),
        };
      },
    );
  }
}

class _LoadingWidget extends StatelessWidget {
  const _LoadingWidget();

  @override
  Widget build(BuildContext context) {
    return const Center(child: CircularProgressIndicator());
  }
}

class _ErrorWidget extends StatelessWidget {
  const _ErrorWidget({required this.message, required this.onRetry});

  final String message;
  final VoidCallback onRetry;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48.w, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            message,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16.sp, color: Colors.red),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(onPressed: onRetry, child: const Text('Retry')),
        ],
      ),
    );
  }
}

/// Widget displayed when apps are successfully loaded
class _LoadedWidget extends StatelessWidget {
  const _LoadedWidget({required this.apps, this.updatingAppId});

  final List<SocialAppModel> apps;
  final String? updatingAppId;

  @override
  Widget build(BuildContext context) {
    if (apps.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.apps, size: 48.w, color: Colors.grey),
            SizedBox(height: 16.h),
            Text(
              'No social apps found on this device',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16.sp, color: Colors.grey),
            ),
            SizedBox(height: 8.h),
            Text(
              'Install social apps to see them here',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: apps.length,
      itemBuilder: (context, index) {
        final app = apps[index];
        final isUpdating = updatingAppId == app.id;

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 3.h),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: isUpdating ? Colors.grey[100] : null,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Transform.scale(
                  scale: 0.7,
                  child: isUpdating
                      ? SizedBox(
                          width: 24.w,
                          height: 24.h,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                          ),
                        )
                      : Switch(
                          value: app.isEnabled,
                          onChanged: (val) => context
                              .read<AppsToggleCubit>()
                              .toggleApp(app.id, val),
                          activeColor: Colors.blueAccent,
                          inactiveThumbColor: Colors.grey,
                        ),
                ),
                Opacity(opacity: isUpdating ? 0.6 : 1.0, child: app.iconWidget),
              ],
            ),
          ),
        );
      },
    );
  }
}
