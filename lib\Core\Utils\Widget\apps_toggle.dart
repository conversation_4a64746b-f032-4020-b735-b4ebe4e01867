import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../Storage/Services/social_apps_storage_service.dart';
import '../../Storage/Models/social_app_model.dart';

class AppsToggle extends StatefulWidget {
  const AppsToggle({super.key});

  @override
  State<AppsToggle> createState() => _AppsToggleState();
}

class _AppsToggleState extends State<AppsToggle> {
  List<SocialAppModel> socialApps = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSocialApps();
  }

  /// Load social apps from storage
  Future<void> _loadSocialApps() async {
    try {
      final apps = SocialAppsStorageService.getAllSocialApps();
      setState(() {
        socialApps = apps;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading social apps: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  /// Handle toggle state change for a social app
  Future<void> _onToggleChanged(String appId, bool newValue) async {
    try {
      final success = await SocialAppsStorageService.updateAppToggleState(
        appId,
        newValue,
      );
      if (success) {
        // Update local state
        setState(() {
          final appIndex = socialApps.indexWhere((app) => app.id == appId);
          if (appIndex != -1) {
            socialApps[appIndex] = socialApps[appIndex].copyWith(
              isEnabled: newValue,
            );
          }
        });
      } else {
        // Show error message if update failed
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to update app preference'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error updating toggle state: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('An error occurred while updating preferences'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (socialApps.isEmpty) {
      return const Center(child: Text('No social apps available'));
    }

    return ListView.builder(
      itemCount: socialApps.length,
      itemBuilder: (context, index) {
        final app = socialApps[index];
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 3.h),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Transform.scale(
                  scale: 0.7,
                  child: Switch(
                    value: app.isEnabled,
                    onChanged: (val) => _onToggleChanged(app.id, val),
                    activeColor: Colors.blueAccent,
                    inactiveThumbColor: Colors.grey,
                  ),
                ),
                app.iconWidget,
              ],
            ),
          ),
        );
      },
    );
  }
}
