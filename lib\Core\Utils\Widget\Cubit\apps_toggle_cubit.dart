import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';

import '../../../Services/app_detection_service.dart';
import '../../../Storage/Models/social_app_model.dart';
import '../../../Storage/Local/social_apps_storage_service.dart';

part 'apps_toggle_state.dart';

class AppsToggleCubit extends Cubit<AppsToggleState> {
  AppsToggleCubit() : super(AppsToggleInitial());

  Future<void> loadApps() async {
    try {
      emit(AppsToggleLoading());
      final allApps = SocialAppsStorageService.getAllSocialApps();

      // final isAppDetectionSupported =
      //     AppDetectionService.isAppDetectionSupported;

      Map<String, bool> appInstallationStatus = {};
      List<SocialAppModel> installedApps = [];

      // if (isAppDetectionSupported) {
        // Detect which apps are actually installed
        final installedAppNames =
            await AppDetectionService.getInstalledSocialApps();

        // Create installation status map
        for (final app in allApps) {
          final isInstalled = installedAppNames.contains(app.name);
          appInstallationStatus[app.name] = isInstalled;

          if (isInstalled) {
            installedApps.add(app);
          }
        }
      // } 

      emit(
        AppsToggleLoaded(
          allApps: allApps,
          installedApps: installedApps,
          isAppDetectionSupported: true,
          appInstallationStatus: appInstallationStatus,
        ),
      );
    } catch (e) {
      emit(
        AppsToggleError(message: 'Failed to load social apps: ${e.toString()}'),
      );
    }
  }

  /// Toggle the state of a specific app
  Future<void> toggleApp(String appId, bool newValue) async {
    final currentState = state;
    if (currentState is! AppsToggleLoaded) {
      return;
    }

    try {
      // Emit updating state
      emit(AppsToggleUpdating(loadedState: currentState, updatingAppId: appId));

      // Update the app in storage
      final success = await SocialAppsStorageService.updateAppToggleState(
        appId,
        newValue,
      );

      if (success) {
        final updatedAllApps = SocialAppsStorageService.getAllSocialApps();

        final updatedInstalledApps = updatedAllApps
            .where(
              (app) => currentState.appInstallationStatus[app.name] == true,
            )
            .toList();

      
        emit(
          currentState.copyWith(
            allApps: updatedAllApps,
            installedApps: updatedInstalledApps,
          ),
        );
      } else {
        emit(
          AppsToggleError(
            message: 'Failed to update app preference',
            previousState: currentState,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error toggling app state: $e');
      emit(
        AppsToggleError(
          message:
              'An error occurred while updating preferences: ${e.toString()}',
          previousState: currentState,
        ),
      );
    }
  }



  /// Recover from error state
  void recoverFromError() {
    final currentState = state;
    if (currentState is AppsToggleError && currentState.previousState != null) {
      emit(currentState.previousState!);
    } else {
      // If no previous state, reload apps
      loadApps();
    }
  }


}
