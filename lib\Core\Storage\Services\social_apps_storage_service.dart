import 'package:hive/hive.dart';
import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';

import '../Models/social_app_model.dart';

/// Service class for managing social apps preferences using Hive local storage
/// Handles saving, loading, updating, and resetting social app toggle states
class SocialAppsStorageService {
  static const String _socialAppsBoxKey = 'social_apps_preferences';
  static late Box<SocialAppModel> _socialAppsBox;

  /// Initialize the social apps storage service
  /// Must be called before using any other methods
  static Future<void> init() async {
    try {
      _socialAppsBox = await Hive.openBox<SocialAppModel>(_socialAppsBoxKey);

      // Initialize with default data if box is empty
      if (_socialAppsBox.isEmpty) {
        await _initializeDefaultApps();
      }
    } catch (e) {
      debugPrint('Error initializing SocialAppsStorageService: $e');
      rethrow;
    }
  }

  /// Get all social apps with their current toggle states
  /// Returns a list of SocialAppModel objects
  static List<SocialAppModel> getAllSocialApps() {
    try {
      return _socialAppsBox.values.toList()
        ..sort((a, b) => a.name.compareTo(b.name));
    } catch (e) {
      debugPrint('Error getting all social apps: $e');
      return [];
    }
  }

  /// Get a specific social app by its ID
  /// Returns null if the app is not found
  static SocialAppModel? getSocialAppById(String id) {
    try {
      return _socialAppsBox.get(id);
    } catch (e) {
      debugPrint('Error getting social app by ID ($id): $e');
      return null;
    }
  }

  /// Get a specific social app by its name
  /// Returns null if the app is not found
  static SocialAppModel? getSocialAppByName(String name) {
    try {
      return _socialAppsBox.values.firstWhere(
        (app) => app.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      debugPrint('Error getting social app by name ($name): $e');
      return null;
    }
  }

  /// Update the toggle state of a specific social app
  /// Returns true if the update was successful, false otherwise
  static Future<bool> updateAppToggleState(String appId, bool isEnabled) async {
    try {
      final app = _socialAppsBox.get(appId);
      if (app != null) {
        final updatedApp = app.copyWith(
          isEnabled: isEnabled,
          lastUpdated: DateTime.now(),
        );
        await _socialAppsBox.put(appId, updatedApp);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating app toggle state for $appId: $e');
      return false;
    }
  }

  /// Save or update a social app model
  /// Returns true if the operation was successful, false otherwise
  static Future<bool> saveSocialApp(SocialAppModel app) async {
    try {
      await _socialAppsBox.put(app.id, app);
      return true;
    } catch (e) {
      debugPrint('Error saving social app ${app.name}: $e');
      return false;
    }
  }

  /// Get all enabled social apps
  /// Returns a list of SocialAppModel objects that are currently enabled
  static List<SocialAppModel> getEnabledApps() {
    try {
      return _socialAppsBox.values.where((app) => app.isEnabled).toList()
        ..sort((a, b) => a.name.compareTo(b.name));
    } catch (e) {
      debugPrint('Error getting enabled apps: $e');
      return [];
    }
  }

  /// Get all disabled social apps
  /// Returns a list of SocialAppModel objects that are currently disabled
  static List<SocialAppModel> getDisabledApps() {
    try {
      return _socialAppsBox.values.where((app) => !app.isEnabled).toList()
        ..sort((a, b) => a.name.compareTo(b.name));
    } catch (e) {
      debugPrint('Error getting disabled apps: $e');
      return [];
    }
  }

  /// Reset all social apps to their default state (all disabled)
  /// Returns true if the reset was successful, false otherwise
  static Future<bool> resetToDefaults() async {
    try {
      await _socialAppsBox.clear();
      await _initializeDefaultApps();
      return true;
    } catch (e) {
      debugPrint('Error resetting social apps to defaults: $e');
      return false;
    }
  }

  /// Enable all social apps
  /// Returns true if the operation was successful, false otherwise
  static Future<bool> enableAllApps() async {
    try {
      final apps = _socialAppsBox.values.toList();
      for (final app in apps) {
        final updatedApp = app.copyWith(
          isEnabled: true,
          lastUpdated: DateTime.now(),
        );
        await _socialAppsBox.put(app.id, updatedApp);
      }
      return true;
    } catch (e) {
      debugPrint('Error enabling all apps: $e');
      return false;
    }
  }

  /// Disable all social apps
  /// Returns true if the operation was successful, false otherwise
  static Future<bool> disableAllApps() async {
    try {
      final apps = _socialAppsBox.values.toList();
      for (final app in apps) {
        final updatedApp = app.copyWith(
          isEnabled: false,
          lastUpdated: DateTime.now(),
        );
        await _socialAppsBox.put(app.id, updatedApp);
      }
      return true;
    } catch (e) {
      debugPrint('Error disabling all apps: $e');
      return false;
    }
  }

  /// Get the count of enabled apps
  static int getEnabledAppsCount() {
    try {
      return _socialAppsBox.values.where((app) => app.isEnabled).length;
    } catch (e) {
      debugPrint('Error getting enabled apps count: $e');
      return 0;
    }
  }

  /// Check if a specific app is enabled
  static bool isAppEnabled(String appId) {
    try {
      final app = _socialAppsBox.get(appId);
      return app?.isEnabled ?? false;
    } catch (e) {
      debugPrint('Error checking if app is enabled ($appId): $e');
      return false;
    }
  }

  /// Convert stored models to the format expected by the existing AppsToggle widget
  /// Returns a list of `Map<String, dynamic>` for backward compatibility
  static List<Map<String, dynamic>> getSocialAppsAsMapList() {
    try {
      return _socialAppsBox.values.map((app) => app.toMap()).toList()
        ..sort((a, b) => (a['name'] as String).compareTo(b['name'] as String));
    } catch (e) {
      debugPrint('Error converting social apps to map list: $e');
      return [];
    }
  }

  /// Initialize the box with default social apps data
  static Future<void> _initializeDefaultApps() async {
    final defaultApps = _getDefaultSocialApps();

    for (final appData in defaultApps) {
      final app = SocialAppModel.fromMap(appData);
      await _socialAppsBox.put(app.id, app);
    }
  }

  /// Get the default social apps configuration
  static List<Map<String, dynamic>> _getDefaultSocialApps() {
    return [
      {
        "value": false,
        'name': 'WhatsApp',
        'icon': Icon(Bootstrap.whatsapp, color: Color(0xFF25D366)),
      },
      {
        "value": false,
        'name': 'Messenger',
        'icon': Icon(Bootstrap.messenger, color: Color(0xFF0078FF)),
      },
      {
        "value": false,
        'name': 'Telegram',
        'icon': Icon(Bootstrap.telegram, color: Color(0xFF0088CC)),
      },
      {
        "value": false,
        'name': 'Instagram',
        'icon': Icon(Bootstrap.instagram, color: Color(0xFFE4405F)),
      },
      {
        "value": false,
        'name': 'Facebook',
        'icon': Icon(Bootstrap.facebook, color: Color(0xFF1877F2)),
      },
      {
        "value": false,
        'name': 'iMessage',
        'icon': Icon(Bootstrap.chat_left_text_fill, color: Color(0xFF32CD32)),
      },
      {
        "value": false,
        'name': 'Botim',
        'icon': Icon(Bootstrap.chat_dots, color: Color(0xFF00BAF2)),
      },
      {
        "value": false,
        'name': 'IMO',
        'icon': Icon(Bootstrap.chat, color: Color(0xFF00AEEF)),
      },
      {
        "value": false,
        'name': 'Snapchat',
        'icon': Icon(Bootstrap.snapchat, color: Color(0xFFFFFC00)),
      },
      {
        "value": false,
        'name': 'Viber',
        'icon': Icon(Bootstrap.phone_vibrate, color: Color(0xFF7360F2)),
      },
      {
        "value": false,
        'name': 'Google Chat',
        'icon': Icon(Bootstrap.google, color: Color(0xFF25AF5E)),
      },
      {
        "value": false,
        'name': 'LINE',
        'icon': Icon(Bootstrap.chat_left_text, color: Color(0xFF00C300)),
      },
    ];
  }

  /// Close the storage service and clean up resources
  static Future<void> dispose() async {
    try {
      await _socialAppsBox.close();
    } catch (e) {
      debugPrint('Error disposing SocialAppsStorageService: $e');
    }
  }
}
